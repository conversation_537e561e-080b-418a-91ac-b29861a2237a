openapi: 3.0.3
info:
  title: Pepe Auth API
  description: Next-generation licensing & authentication platform API
  version: 1.0.0
  contact:
    name: <PERSON><PERSON><PERSON> Auth Support
    email: <EMAIL>
    url: https://docs.pepe-auth.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.pepe-auth.com/v1
    description: Production server
  - url: https://staging-api.pepe-auth.com/v1
    description: Staging server
  - url: http://localhost:5000/api/v1
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  # Authentication endpoints
  /auth/register:
    post:
      tags: [Authentication]
      summary: Register a new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /auth/login:
    post:
      tags: [Authentication]
      summary: Authenticate user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimit'

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
              required: [refresh_token]
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/logout:
    post:
      tags: [Authentication]
      summary: Logout user
      responses:
        '200':
          description: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'

  # License validation endpoints
  /auth/validate:
    post:
      tags: [License Validation]
      summary: Validate license key
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateRequest'
      responses:
        '200':
          description: License validation result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '429':
          $ref: '#/components/responses/RateLimit'

  # License management endpoints
  /licenses:
    get:
      tags: [License Management]
      summary: List user licenses
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, expired, suspended, revoked]
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of licenses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseList'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags: [License Management]
      summary: Create new license
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLicenseRequest'
      responses:
        '201':
          description: License created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/License'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /licenses/{licenseId}:
    get:
      tags: [License Management]
      summary: Get license details
      parameters:
        - name: licenseId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: License details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/License'
        '404':
          $ref: '#/components/responses/NotFound'

    patch:
      tags: [License Management]
      summary: Update license
      parameters:
        - name: licenseId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLicenseRequest'
      responses:
        '200':
          description: License updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/License'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [License Management]
      summary: Revoke license
      parameters:
        - name: licenseId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: License revoked successfully
        '404':
          $ref: '#/components/responses/NotFound'

  # Analytics endpoints
  /analytics/dashboard:
    get:
      tags: [Analytics]
      summary: Get dashboard analytics
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [24h, 7d, 30d, 90d]
            default: 7d
      responses:
        '200':
          description: Dashboard analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardAnalytics'

  /analytics/validations:
    get:
      tags: [Analytics]
      summary: Get validation analytics
      parameters:
        - name: period
          in: query
          schema:
            type: string
            enum: [24h, 7d, 30d, 90d]
            default: 7d
        - name: license_id
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Validation analytics data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationAnalytics'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    RegisterRequest:
      type: object
      required: [email, username, password]
      properties:
        email:
          type: string
          format: email
        username:
          type: string
          minLength: 3
          maxLength: 50
        password:
          type: string
          minLength: 8
        first_name:
          type: string
          maxLength: 100
        last_name:
          type: string
          maxLength: 100

    LoginRequest:
      type: object
      required: [login, password]
      properties:
        login:
          type: string
          description: Email or username
        password:
          type: string
        totp_code:
          type: string
          pattern: '^[0-9]{6}$'

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        user:
          $ref: '#/components/schemas/User'

    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
        expires_in:
          type: integer

    ValidateRequest:
      type: object
      required: [license_key, hwid, timestamp, signature]
      properties:
        license_key:
          type: string
        hwid:
          type: string
        timestamp:
          type: integer
          format: int64
        signature:
          type: string

    ValidateResponse:
      type: object
      properties:
        valid:
          type: boolean
        result:
          type: string
          enum: [success, invalid_key, expired, suspended, hwid_mismatch, ip_blocked, rate_limited]
        message:
          type: string
        expires_at:
          type: string
          format: date-time
        plan:
          $ref: '#/components/schemas/Plan'
        cdn_token:
          type: string
        update_manifest:
          type: object

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        username:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        role:
          type: string
          enum: [owner, admin, billing, support, readonly]
        is_verified:
          type: boolean
        totp_enabled:
          type: boolean
        created_at:
          type: string
          format: date-time

    License:
      type: object
      properties:
        id:
          type: string
          format: uuid
        license_key:
          type: string
        status:
          type: string
          enum: [active, expired, suspended, revoked]
        plan:
          $ref: '#/components/schemas/Plan'
        hwid_lock:
          type: string
        max_devices:
          type: integer
        current_devices:
          type: integer
        expires_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time

    Plan:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        type:
          type: string
          enum: [free_trial, hobby, pro, enterprise]
        price_monthly:
          type: number
          format: decimal
        max_licenses:
          type: integer
        max_devices_per_license:
          type: integer
        features:
          type: object

    CreateLicenseRequest:
      type: object
      required: [plan_id]
      properties:
        plan_id:
          type: string
          format: uuid
        hwid_lock:
          type: string
        max_devices:
          type: integer
        expires_at:
          type: string
          format: date-time

    UpdateLicenseRequest:
      type: object
      properties:
        status:
          type: string
          enum: [active, suspended, revoked]
        hwid_lock:
          type: string
        max_devices:
          type: integer
        expires_at:
          type: string
          format: date-time

    LicenseList:
      type: object
      properties:
        licenses:
          type: array
          items:
            $ref: '#/components/schemas/License'
        total:
          type: integer
        limit:
          type: integer
        offset:
          type: integer

    DashboardAnalytics:
      type: object
      properties:
        total_licenses:
          type: integer
        active_licenses:
          type: integer
        total_validations:
          type: integer
        successful_validations:
          type: integer
        validation_success_rate:
          type: number
          format: percentage
        monthly_revenue:
          type: number
          format: decimal

    ValidationAnalytics:
      type: object
      properties:
        total_validations:
          type: integer
        success_rate:
          type: number
          format: percentage
        error_breakdown:
          type: object
        geographic_distribution:
          type: object
        hourly_distribution:
          type: array
          items:
            type: object

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    RateLimit:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
