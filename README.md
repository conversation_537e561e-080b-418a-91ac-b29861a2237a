# Pepe Auth - Next-Generation Licensing & Authentication Platform

A secure, scalable, and feature-rich licensing and authentication platform built with modern technologies and a stunning glassmorphism UI. Think "KeyAuth" but more secure, more insightful, and designed for 2025.

## 🚀 Features

### ✅ Implemented (Phase 1 & 2)
- **🔐 Core Authentication**: User registration, login, logout with Flask-Login
- **🎨 Glassmorphism UI**: Beautiful, modern web interface with Tailwind CSS
- **📊 Dashboard**: Real-time overview of licenses and validations
- **🔑 License Management**: Create, view, and manage license keys
- **📈 Analytics**: Basic validation tracking and success rate monitoring
- **⚙️ Settings**: User profile and security settings interface
- **🗄️ SQLite Database**: Instant setup with no external dependencies
- **🚀 One-Click Start**: Simple `python start.py` to get running

### 🚧 Coming Soon (Phase 3-5)
- **Multi-factor Authentication**: TOTP 2FA implementation
- **Hardware Fingerprinting**: HWID locking and device management
- **License Validation API**: Secure validation endpoints with HMAC
- **Advanced Analytics**: Charts, geographic data, fraud detection
- **API Key Management**: Programmatic access tokens
- **Webhook System**: Real-time event notifications
- **Plan Management**: Subscription tiers and billing integration

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Flask Templates + TailwindCSS + Glassmorphism Design
- **Backend**: Python 3.12 + Flask 3 + SQLAlchemy + Flask-Login
- **Database**: SQLite (instant setup, no configuration needed)
- **Security**: Argon2 password hashing, CSRF protection, secure sessions
- **Deployment**: Single Python file execution, no containers needed

### Project Structure
```
/src
  /frontend          # React TypeScript application
    /src
      /components    # Reusable UI components
      /pages         # Page components
      /hooks         # Custom React hooks
      /utils         # Utility functions
      /types         # TypeScript type definitions
    /public          # Static assets
  /backend           # Flask API server
    /app             # Application factory and config
    /models          # SQLAlchemy models
    /routes          # API route handlers
    /services        # Business logic services
    /utils           # Backend utilities
  /shared            # Shared DTOs and constants
/config              # Environment and secrets templates
/deploy              # Docker and Kubernetes configurations
  /docker            # Dockerfiles and compose files
  /k8s               # Kubernetes manifests and Helm charts
/scripts             # CLI tools and database migrations
  /db                # Database migration scripts
  /cli               # Command-line utilities
/docs                # Documentation and API specs
/tests               # Test suites
  /unit              # Unit tests
  /integration       # Integration tests
  /e2e               # End-to-end tests
```

## 🎨 Glassmorphism UI Design

Our 2025-grade glassmorphism interface features:
- **Frosted Glass Effects**: 8-16px blur with translucent surfaces
- **High Contrast**: Strategic use of backplates for readability
- **Micro-animations**: Framer Motion with <200ms transitions
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Performance Optimized**: Backdrop-filter only on static elements

## 🚦 Getting Started

### Prerequisites
- Python 3.8+ (that's it!)

### ⚡ Instant Setup (30 seconds)
```bash
# Clone the repository
git clone <repository-url>
cd pepe-auth

# Run the magic startup script
python start.py
```

That's it! The script will:
1. 📦 Install all Python dependencies automatically
2. 🗄️ Set up SQLite database with sample data
3. 👤 Create admin user (<EMAIL> / admin123)
4. 🚀 Start the server at http://localhost:5000

### Manual Setup (if you prefer)
```bash
# Install dependencies
cd src/backend
pip install -r requirements.txt

# Initialize database
flask init-db
flask create-admin --email <EMAIL> --username admin --password admin123

# Start server
python run.py
```

## 📊 API Documentation

Interactive API documentation is available at `/docs` when running the development server.

Key endpoints:
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/validate` - License validation
- `GET /api/v1/licenses` - License management
- `GET /api/v1/analytics/dashboard` - Analytics data

## 🔒 Security Features

- **Rate Limiting**: Global and per-endpoint quotas
- **Anomaly Detection**: ML-based fraud detection
- **Secure Headers**: HSTS, CSP, and security headers
- **Input Validation**: Comprehensive request validation
- **Audit Logging**: Complete action audit trail

## 🧪 Testing

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest --cov=src/backend tests/

# Run e2e tests
playwright test
```

## 📈 Deployment

### Production Deployment
```bash
# Build and deploy with Kubernetes
kubectl apply -f deploy/k8s/

# Or use Helm
helm install pepe-auth deploy/helm/
```

### Self-hosted Deployment
```bash
# Single-node Docker Compose
docker-compose -f deploy/docker/docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/pepe-auth)
- 📖 Documentation: [docs.pepe-auth.com](https://docs.pepe-auth.com)
- 🐛 Issues: [GitHub Issues](https://github.com/pepe-auth/issues)

---

Built with ❤️ by the Pepe Auth team
