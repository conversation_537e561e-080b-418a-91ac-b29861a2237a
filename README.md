# 🐸 Pepe Auth - Lightweight Licensing & Authentication Platform

A secure, self-hosted licensing platform that **outclasses KeyAuth** while staying simple to run on a single VPS. Built with Python 3.12 + Flask 3 + SQLite + Vanilla JS with a stunning glassmorphism UI.

## ✨ Why Pepe Auth?

**Pepe Auth** delivers everything you need for professional software licensing:
- 🔐 **Military-grade security** with HMAC-SHA256, Argon2, TOTP 2FA
- 📱 **Offline licensing** with signed JSON files (30-day validity)
- 📊 **Built-in analytics** with fraud detection and geographic tracking
- ⚡ **Lightning fast** - Deploy in under 60 seconds, no Docker needed
- 🎨 **Beautiful glassmorphism UI** with dark/light themes
- 🏷️ **White-label ready** with custom branding and themes

## 🚀 Features Implemented

### ✅ Core Platform
- **🔐 Authentication System**: Argon2 password hashing, JWT tokens, optional TOTP 2FA
- **👥 Role-based Access**: Owner, Admin, Support, User roles with granular permissions
- **🔑 License Engine**: HMAC-signed keys with hardware fingerprinting
- **📱 Offline Licensing**: Signed JSON files for 30-day offline validation
- **⚡ Validation API**: Secure `/api/validate` endpoint with timestamp verification
- **📊 Analytics Dashboard**: Real-time usage tracking and fraud detection
- **🛡️ Security Features**: Rate limiting (30 req/min), blacklist management, admin re-auth

### ✅ Management Tools
- **📋 License Management**: Create, extend, monitor license keys
- **💰 Plan System**: Subscription tiers with device/API limits
- **🎁 Referral System**: Tracking codes with reward mechanisms
- **🎉 Giveaway Engine**: Automated winner selection algorithms
- **📈 Event System**: Promotional campaigns and feature toggles
- **🎨 Theme Customization**: White-label branding with custom CSS

### ✅ User Experience
- **🎨 Glassmorphism UI**: Beautiful frosted glass effects with 4.5:1 contrast
- **🌙 Dark/Light Themes**: Automatic theme switching with localStorage
- **📱 Responsive Design**: Mobile-first approach, works on all devices
- **⚡ Vanilla JavaScript**: No build tools, no React/Vue complexity
- **🔍 Real-time Search**: Instant filtering and live updates

## 🏗️ Architecture

### Technology Stack
- **Backend**: Python 3.12 + Flask 3 + SQLite (no ORM complexity)
- **Frontend**: Jinja2 templates + Vanilla JS + CSS glassmorphism
- **Security**: HMAC-SHA256, Argon2, itsdangerous JWT, pyotp TOTP
- **Database**: SQLite with parameterized queries (no external dependencies)
- **Deployment**: Single VPS, no Docker/Kubernetes required

### Project Structure
```
/app.py              # Main Flask application
/models.py           # Database models and SQLite helpers
/migrations.sql      # Database schema
/requirements.txt    # Python dependencies
/run.py              # Quick start script
/test_basic.py       # Test suite

/templates/          # Jinja2 templates
  /base.html         # Base template with glassmorphism
  /index.html        # Landing page
  /login.html        # Authentication
  /dashboard.html    # User dashboard
  /licenses.html     # License management
  /admin/            # Admin interface

/static/             # Static assets
  /css/glass.css     # Glassmorphism framework
  /js/pepe-auth.js   # Core JavaScript utilities
```

## 🚦 Quick Start (60 seconds)

### Prerequisites
- Python 3.8+ (that's it!)

### ⚡ One-Command Setup
```bash
# Clone and start
git clone <repository-url>
cd pepe-auth
python run.py
```

The script automatically:
1. 📦 Installs Python dependencies
2. 🗄️ Creates SQLite database with schema
3. 👤 Creates admin user (<EMAIL> / admin123)
4. 🚀 Starts server at http://localhost:5000

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python app.py
```

## 🔐 Security Features

### Authentication & Authorization
- **Argon2 Password Hashing**: Industry-standard password security
- **JWT Session Tokens**: Secure, stateless authentication
- **TOTP 2FA Support**: Optional two-factor authentication
- **Role-based Access Control**: Granular permission system
- **Admin Re-authentication**: Destructive actions require recent login

### License Protection
- **HMAC-SHA256 Signatures**: Cryptographically signed license keys
- **Hardware Fingerprinting**: Device-specific license binding
- **Timestamp Validation**: ±120 second tolerance for clock skew
- **Blacklist Management**: IP/HWID/Email blocking with expiration
- **Rate Limiting**: 30 requests/minute per IP (configurable)

### Data Protection
- **Parameterized SQL Queries**: SQL injection prevention
- **CSRF Protection**: Cross-site request forgery prevention
- **Secure Headers**: HSTS, CSP, and security headers
- **Audit Logging**: Complete action audit trail
- **Geographic Tracking**: GeoLite2 integration for fraud detection

## 📊 API Documentation

### Core License Validation
```bash
POST /api/validate
Content-Type: application/json

{
  "key": "license-key-here",
  "hwid": "hardware-fingerprint",
  "ts": **********,
  "sig": "hmac-sha256-signature"
}

# Success Response
{
  "status": "ok",
  "session_token": "jwt-token",
  "cdn_token": "signed-download-url",
  "expires_at": "2024-12-31T23:59:59",
  "plan": "Pro"
}
```

### Offline License Generation
```bash
GET /api/license/{id}/offline
Authorization: Bearer jwt-token

# Response: Signed JSON file
{
  "data": {
    "license_key": "...",
    "expires_at": "...",
    "max_devices": 3,
    "offline_valid_until": "..."
  },
  "signature": "hmac-signature",
  "version": "1.0"
}
```

### Key Endpoints
- `POST /api/validate` - License validation with HMAC verification
- `GET /api/license/{id}/offline` - Generate offline license file
- `POST /login` - User authentication
- `POST /register` - User registration
- `GET /dashboard` - User dashboard (requires auth)
- `GET /admin` - Admin panel (requires admin role)

## 🎨 Glassmorphism UI

### Design System
- **Glass Effects**: 10-20px blur with translucent surfaces
- **Color Palette**: CSS variables for easy theming
- **Typography**: Inter font with perfect contrast ratios
- **Animations**: Subtle 200ms transitions for smooth interactions
- **Accessibility**: 4.5:1 contrast ratio compliance

### Theme Customization
```css
:root {
  --primary-color: #3b82f6;
  --glass-blur: 10px;
  --glass-opacity: 0.1;
  --border-radius: 16px;
}

[data-theme="dark"] {
  --glass-opacity: 0.15;
  --text-primary: #f9fafb;
}
```

### Components
- **Glass Cards**: Frosted containers with hover effects
- **Glass Buttons**: Interactive elements with backdrop blur
- **Glass Forms**: Input fields with focus animations
- **Glass Navigation**: Sticky header with transparency
- **Glass Modals**: Overlay dialogs with blur backdrop

## 🧪 Testing

### Run Tests
```bash
# Basic functionality tests
python test_basic.py

# With verbose output
python -m unittest test_basic.py -v
```

### Test Coverage
- ✅ Authentication flow (login/register/logout)
- ✅ License validation API
- ✅ Rate limiting functionality
- ✅ HMAC signature generation/verification
- ✅ JWT token creation/validation
- ✅ Database initialization
- ✅ Admin access control
- ✅ Dashboard authentication

## 📈 Deployment

### Single VPS Deployment (Recommended)
```bash
# 1. Clone repository
git clone <repo-url>
cd pepe-auth

# 2. Install dependencies
pip install -r requirements.txt

# 3. Configure environment
export SECRET_KEY="your-secret-key"
export HMAC_SECRET="your-hmac-secret"
export JWT_SECRET="your-jwt-secret"

# 4. Run with production server
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:create_app()
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### Environment Variables
```bash
# Security (required)
SECRET_KEY=your-flask-secret-key
HMAC_SECRET=your-hmac-signing-secret
JWT_SECRET=your-jwt-signing-secret

# Database (optional)
DATABASE_PATH=/path/to/pepe_auth.db

# Rate Limiting (optional)
RATE_LIMIT_PER_MINUTE=30
ADMIN_REAUTH_TIMEOUT=300
SESSION_TIMEOUT=3600
```

## 🔧 Configuration

### Default Plans
The system comes with 4 pre-configured plans:
- **Free**: $0, 1 device, 100 API calls/month
- **Basic**: $9.99, 3 devices, 1,000 API calls/month
- **Pro**: $19.99, 10 devices, 10,000 API calls/month
- **Enterprise**: $49.99, 100 devices, 100,000 API calls/month

### Customization Options
- **Branding**: Logo, colors, custom CSS
- **Plans**: Pricing, limits, duration
- **Security**: Rate limits, session timeouts
- **Features**: Enable/disable referrals, giveaways
- **Analytics**: Retention periods, geographic tracking

## 🆚 Comparison with Competitors

| Feature | Pepe Auth | KeyAuth | Cryptolens |
|---------|-----------|---------|------------|
| Offline Licensing | ✅ | ❌ | ✅ |
| Built-in Analytics | ✅ | ⚠️ | ✅ |
| Rate Limiting | ✅ | ❌ | ⚠️ |
| Self-Hosted | ✅ | ⚠️ | ⚠️ |
| Modern UI | ✅ | ❌ | ⚠️ |
| Setup Time | < 60s | ~5min | ~15min |
| No Docker Required | ✅ | ❌ | ❌ |
| White-label Ready | ✅ | ⚠️ | ✅ |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 for Python code
- Use semantic commit messages
- Add tests for new features
- Update documentation
- Maintain 4.5:1 contrast ratio for UI

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Community

- 🐛 **Issues**: [GitHub Issues](https://github.com/pepe-auth/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/pepe-auth/discussions)
- 📖 **Documentation**: [Wiki](https://github.com/pepe-auth/wiki)
- 📧 **Email**: <EMAIL>

---

**Built with ❤️ for developers who need reliable, secure, and beautiful licensing solutions.**

*Pepe Auth - Because your software deserves better than KeyAuth.*
