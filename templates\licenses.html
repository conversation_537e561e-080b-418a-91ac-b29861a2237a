{% extends "base.html" %}

{% block title %}Licenses - <PERSON><PERSON><PERSON>th{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 700; color: var(--text-white); margin-bottom: 0.5rem;">
                License Management
            </h1>
            <p style="color: var(--text-secondary); font-size: 1.1rem;">
                Create, manage, and monitor your license keys
            </p>
        </div>
        
        <button onclick="openCreateModal()" class="btn btn-primary" style="font-size: 1rem; padding: 0.75rem 1.5rem;">
            ➕ Create License
        </button>
    </div>
    
    <!-- License Stats -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
        <div class="glass-card" style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔑</div>
            <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-white);">{{ licenses|length }}</h3>
            <p style="color: var(--text-secondary); font-size: 0.9rem;">Total Licenses</p>
        </div>
        
        <div class="glass-card" style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">✅</div>
            <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--success-color);">
                {{ licenses|selectattr('expires_at', '>', moment().utc().isoformat())|list|length }}
            </h3>
            <p style="color: var(--text-secondary); font-size: 0.9rem;">Active</p>
        </div>
        
        <div class="glass-card" style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">⏰</div>
            <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--error-color);">
                {{ licenses|selectattr('expires_at', '<', moment().utc().isoformat())|list|length }}
            </h3>
            <p style="color: var(--text-secondary); font-size: 0.9rem;">Expired</p>
        </div>
        
        <div class="glass-card" style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
            <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-white);">
                {{ licenses|sum(attribute='validation_count') or 0 }}
            </h3>
            <p style="color: var(--text-secondary); font-size: 0.9rem;">Total Validations</p>
        </div>
    </div>
    
    <!-- Licenses Table -->
    <div class="glass-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h2 style="font-size: 1.5rem; font-weight: 600; color: var(--text-white);">
                Your Licenses
            </h2>
            
            <!-- Search and Filter -->
            <div style="display: flex; gap: 1rem; align-items: center;">
                <input 
                    type="text" 
                    id="searchLicenses" 
                    placeholder="Search licenses..." 
                    class="form-input" 
                    style="width: 200px; margin: 0;"
                >
                <select id="filterStatus" class="form-input" style="width: 120px; margin: 0;">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="expired">Expired</option>
                </select>
            </div>
        </div>
        
        {% if licenses %}
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;" id="licensesTable">
                <thead>
                    <tr style="border-bottom: 1px solid var(--border-color);">
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">License Key</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Plan</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Status</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Created</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Expires</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Devices</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Validations</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for license in licenses %}
                    <tr style="border-bottom: 1px solid var(--border-color);" class="license-row" 
                        data-key="{{ license.license_key }}" 
                        data-plan="{{ license.plan_name }}"
                        data-status="{% if license.expires_at > moment().utc().isoformat() %}active{% else %}expired{% endif %}">
                        
                        <td style="padding: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <code style="color: var(--text-secondary); font-family: monospace; font-size: 0.9rem;">
                                    {{ license.license_key }}
                                </code>
                                <button onclick="copyToClipboard('{{ license.license_key }}')" 
                                        style="background: none; border: none; color: var(--text-light); cursor: pointer; padding: 0.25rem;"
                                        title="Copy license key">
                                    📋
                                </button>
                            </div>
                        </td>
                        
                        <td style="padding: 1rem; color: var(--text-secondary);">
                            <span style="background: var(--bg-secondary); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                {{ license.plan_name }}
                            </span>
                        </td>
                        
                        <td style="padding: 1rem;">
                            {% if license.expires_at > moment().utc().isoformat() %}
                                <span style="color: var(--success-color); font-weight: 500;">✅ Active</span>
                            {% else %}
                                <span style="color: var(--error-color); font-weight: 500;">❌ Expired</span>
                            {% endif %}
                        </td>
                        
                        <td style="padding: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                            {{ license.created_at[:10] }}
                        </td>
                        
                        <td style="padding: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                            {{ license.expires_at[:10] }}
                        </td>
                        
                        <td style="padding: 1rem; color: var(--text-secondary);">
                            <span style="font-weight: 500;">0</span> / {{ license.max_devices }}
                        </td>
                        
                        <td style="padding: 1rem; color: var(--text-secondary); font-weight: 500;">
                            {{ license.validation_count or 0 }}
                        </td>
                        
                        <td style="padding: 1rem;">
                            <div style="display: flex; gap: 0.5rem;">
                                <button onclick="viewLicense('{{ license.id }}')" 
                                        class="btn btn-secondary" 
                                        style="padding: 0.5rem; font-size: 0.8rem;"
                                        title="View details">
                                    👁️
                                </button>
                                <button onclick="downloadOffline('{{ license.id }}')" 
                                        class="btn btn-secondary" 
                                        style="padding: 0.5rem; font-size: 0.8rem;"
                                        title="Download offline license">
                                    📱
                                </button>
                                <button onclick="extendLicense('{{ license.id }}')" 
                                        class="btn btn-secondary" 
                                        style="padding: 0.5rem; font-size: 0.8rem;"
                                        title="Extend license">
                                    ⏰
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div style="text-align: center; padding: 4rem; color: var(--text-secondary);">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🔑</div>
            <h3 style="color: var(--text-white); margin-bottom: 1rem;">No licenses found</h3>
            <p style="margin-bottom: 2rem;">Create your first license to start using Pepe Auth</p>
            <button onclick="openCreateModal()" class="btn btn-primary">
                Create Your First License
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create License Modal -->
<div id="createModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(5px);">
    <div style="display: flex; justify-content: center; align-items: center; height: 100%; padding: 1rem;">
        <div class="glass-card" style="width: 100%; max-width: 500px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2 style="color: var(--text-white); font-size: 1.5rem;">Create New License</h2>
                <button onclick="closeCreateModal()" style="background: none; border: none; color: var(--text-light); font-size: 1.5rem; cursor: pointer;">×</button>
            </div>
            
            <form method="POST" action="{{ url_for('create_license') }}">
                <div class="form-group">
                    <label for="plan_id" class="form-label">Plan</label>
                    <select id="plan_id" name="plan_id" class="form-input" required>
                        <option value="">Select a plan</option>
                        {% for plan in plans %}
                        <option value="{{ plan.id }}">
                            {{ plan.name }} - ${{ "%.2f"|format(plan.price) }} 
                            ({{ plan.max_devices }} devices, {{ plan.duration_days }} days)
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="duration_days" class="form-label">Duration (Days)</label>
                    <input type="number" id="duration_days" name="duration_days" class="form-input" value="30" min="1" max="365" required>
                </div>
                
                <div class="form-group">
                    <label for="max_devices" class="form-label">Max Devices</label>
                    <input type="number" id="max_devices" name="max_devices" class="form-input" value="1" min="1" max="100" required>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" onclick="closeCreateModal()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create License</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Modal functions
    function openCreateModal() {
        document.getElementById('createModal').style.display = 'block';
    }
    
    function closeCreateModal() {
        document.getElementById('createModal').style.display = 'none';
    }
    
    // License actions
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // Show temporary success message
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '✅';
            setTimeout(() => btn.textContent = originalText, 1000);
        });
    }
    
    function viewLicense(licenseId) {
        // TODO: Implement license details view
        alert(`View license ${licenseId} - Coming soon!`);
    }
    
    function downloadOffline(licenseId) {
        // TODO: Implement offline license download
        alert(`Download offline license ${licenseId} - Coming soon!`);
    }
    
    function extendLicense(licenseId) {
        // TODO: Implement license extension
        alert(`Extend license ${licenseId} - Coming soon!`);
    }
    
    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchLicenses');
        const statusFilter = document.getElementById('filterStatus');
        const rows = document.querySelectorAll('.license-row');
        
        function filterLicenses() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            
            rows.forEach(row => {
                const key = row.dataset.key.toLowerCase();
                const plan = row.dataset.plan.toLowerCase();
                const status = row.dataset.status;
                
                const matchesSearch = key.includes(searchTerm) || plan.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;
                
                row.style.display = matchesSearch && matchesStatus ? '' : 'none';
            });
        }
        
        searchInput.addEventListener('input', filterLicenses);
        statusFilter.addEventListener('change', filterLicenses);
        
        // Close modal on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCreateModal();
            }
        });
        
        // Close modal on backdrop click
        document.getElementById('createModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCreateModal();
            }
        });
    });
</script>
{% endblock %}
