/**
 * Pepe Auth - Core JavaScript Functionality
 * Vanilla JS utilities for the licensing platform
 */

// Global PepeAuth namespace
window.PepeAuth = {
    // Configuration
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 30000, // 30 seconds
        toastDuration: 5000, // 5 seconds
    },
    
    // Utility functions
    utils: {
        // Copy text to clipboard
        copyToClipboard: async function(text) {
            try {
                await navigator.clipboard.writeText(text);
                PepeAuth.ui.showToast('Copied to clipboard!', 'success');
                return true;
            } catch (err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                PepeAuth.ui.showToast('Copied to clipboard!', 'success');
                return true;
            }
        },
        
        // Format date
        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },
        
        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Generate random string
        generateRandomString: function(length = 16) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
    },
    
    // UI utilities
    ui: {
        // Show toast notification
        showToast: function(message, type = 'info', duration = null) {
            const toast = document.createElement('div');
            toast.className = `flash-message flash-${type}`;
            toast.textContent = message;
            toast.style.animation = 'slideIn 200ms ease';
            
            const container = document.querySelector('.flash-messages') || document.body;
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOut 200ms ease forwards';
                setTimeout(() => toast.remove(), 200);
            }, duration || PepeAuth.config.toastDuration);
        },
        
        // Show loading spinner
        showLoading: function(element) {
            const spinner = document.createElement('div');
            spinner.className = 'glass-spinner';
            spinner.style.margin = '0 auto';
            
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            
            element.innerHTML = '';
            element.appendChild(spinner);
            return spinner;
        },
        
        // Create modal
        createModal: function(title, content, options = {}) {
            const modal = document.createElement('div');
            modal.className = 'glass-modal';
            modal.innerHTML = `
                <div class="glass-modal-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h2 style="color: var(--text-white); margin: 0;">${title}</h2>
                        <button class="modal-close" style="background: none; border: none; color: var(--text-light); font-size: 1.5rem; cursor: pointer;">×</button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            `;
            
            // Close modal functionality
            const closeBtn = modal.querySelector('.modal-close');
            const closeModal = () => {
                modal.style.animation = 'fadeOut 200ms ease forwards';
                setTimeout(() => modal.remove(), 200);
            };
            
            closeBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });
            
            document.addEventListener('keydown', function escapeHandler(e) {
                if (e.key === 'Escape') {
                    closeModal();
                    document.removeEventListener('keydown', escapeHandler);
                }
            });
            
            document.body.appendChild(modal);
            modal.style.animation = 'fadeIn 200ms ease';
            
            return modal;
        },
        
        // Confirm dialog
        confirm: function(message, title = 'Confirm') {
            return new Promise((resolve) => {
                const modal = PepeAuth.ui.createModal(title, `
                    <p style="color: var(--text-secondary); margin-bottom: 2rem;">${message}</p>
                    <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                        <button class="btn btn-secondary cancel-btn">Cancel</button>
                        <button class="btn btn-primary confirm-btn">Confirm</button>
                    </div>
                `);
                
                modal.querySelector('.cancel-btn').addEventListener('click', () => {
                    modal.remove();
                    resolve(false);
                });
                
                modal.querySelector('.confirm-btn').addEventListener('click', () => {
                    modal.remove();
                    resolve(true);
                });
            });
        }
    },
    
    // API utilities
    api: {
        // Make API request
        request: async function(endpoint, options = {}) {
            const url = PepeAuth.config.apiBaseUrl + endpoint;
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                },
            };
            
            const config = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(url, config);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Request failed');
                }
                
                return data;
            } catch (error) {
                PepeAuth.ui.showToast(error.message, 'error');
                throw error;
            }
        },
        
        // Validate license
        validateLicense: async function(licenseKey, hwid) {
            const timestamp = Math.floor(Date.now() / 1000);
            const payload = `${licenseKey}:${hwid}:${timestamp}`;
            
            // Note: In a real implementation, you'd need to generate the HMAC signature
            // This is just a placeholder for the client-side validation
            const signature = 'placeholder_signature';
            
            return PepeAuth.api.request('/validate', {
                method: 'POST',
                body: JSON.stringify({
                    key: licenseKey,
                    hwid: hwid,
                    ts: timestamp,
                    sig: signature
                })
            });
        }
    },
    
    // License management
    license: {
        // Generate hardware ID (simplified version)
        generateHWID: function() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Hardware fingerprint', 2, 2);
            
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                canvas.toDataURL()
            ].join('|');
            
            // Simple hash function
            let hash = 0;
            for (let i = 0; i < fingerprint.length; i++) {
                const char = fingerprint.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            
            return Math.abs(hash).toString(16);
        },
        
        // Download offline license
        downloadOffline: async function(licenseId) {
            try {
                const response = await fetch(`/api/license/${licenseId}/offline`);
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `license_${licenseId}_offline.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                PepeAuth.ui.showToast('Offline license downloaded!', 'success');
            } catch (error) {
                PepeAuth.ui.showToast('Failed to download offline license', 'error');
            }
        }
    },
    
    // Theme management
    theme: {
        // Toggle theme
        toggle: function() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update theme icon
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = newTheme === 'light' ? '🌙' : '☀️';
            }
            
            // Dispatch theme change event
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: newTheme }));
        },
        
        // Load saved theme
        load: function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = savedTheme === 'light' ? '🌙' : '☀️';
            }
        }
    },
    
    // Initialize the application
    init: function() {
        // Load theme
        PepeAuth.theme.load();
        
        // Add global event listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-hide flash messages
            setTimeout(() => {
                const flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(msg => {
                    msg.style.animation = 'slideOut 200ms ease forwards';
                    setTimeout(() => msg.remove(), 200);
                });
            }, PepeAuth.config.toastDuration);
        });
        
        // Add CSS animations if not present
        if (!document.querySelector('#pepe-auth-animations')) {
            const style = document.createElement('style');
            style.id = 'pepe-auth-animations';
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; transform: scale(0.95); }
                    to { opacity: 1; transform: scale(1); }
                }
                @keyframes fadeOut {
                    from { opacity: 1; transform: scale(1); }
                    to { opacity: 0; transform: scale(0.95); }
                }
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        console.log('🐸 Pepe Auth initialized successfully!');
    }
};

// Auto-initialize when script loads
PepeAuth.init();
