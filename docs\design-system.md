# Pepe Auth Design System - Glassmorphism 2025

## 🎨 Color Palette

### Primary Colors
```css
/* Light Theme */
--primary-50: #f0f9ff;
--primary-100: #e0f2fe;
--primary-200: #bae6fd;
--primary-300: #7dd3fc;
--primary-400: #38bdf8;
--primary-500: #0ea5e9;  /* Primary */
--primary-600: #0284c7;
--primary-700: #0369a1;
--primary-800: #075985;
--primary-900: #0c4a6e;

/* Dark Theme */
--primary-dark-50: #0c1420;
--primary-dark-100: #1e293b;
--primary-dark-200: #334155;
--primary-dark-300: #475569;
--primary-dark-400: #64748b;
--primary-dark-500: #94a3b8;
--primary-dark-600: #cbd5e1;
--primary-dark-700: #e2e8f0;
--primary-dark-800: #f1f5f9;
--primary-dark-900: #f8fafc;
```

### Accent Colors
```css
/* Success */
--success-400: #4ade80;
--success-500: #22c55e;
--success-600: #16a34a;

/* Warning */
--warning-400: #facc15;
--warning-500: #eab308;
--warning-600: #ca8a04;

/* Error */
--error-400: #f87171;
--error-500: #ef4444;
--error-600: #dc2626;

/* Info */
--info-400: #60a5fa;
--info-500: #3b82f6;
--info-600: #2563eb;
```

## 🌟 Glassmorphism Effects

### Glass Surface Properties
```css
.glass-surface {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-surface-dark {
  background: rgba(15, 23, 42, 0.3);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(148, 163, 184, 0.1);
}
```

### Blur Variations
```css
.blur-light { backdrop-filter: blur(8px); }
.blur-medium { backdrop-filter: blur(12px); }
.blur-heavy { backdrop-filter: blur(16px); }
.blur-extreme { backdrop-filter: blur(24px); }
```

## 🧩 Component Library

### Card Components
```css
/* Primary Card */
.card-primary {
  @apply glass-surface p-6 transition-all duration-200;
}

.card-primary:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Secondary Card */
.card-secondary {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}
```

### Button Components
```css
/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, 
    rgba(14, 165, 233, 0.8) 0%, 
    rgba(59, 130, 246, 0.8) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, 
    rgba(14, 165, 233, 0.9) 0%, 
    rgba(59, 130, 246, 0.9) 100%);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
}

/* Ghost Button */
.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--primary-600);
  font-weight: 500;
  padding: 12px 24px;
  transition: all 0.2s ease;
}
```

### Input Components
```css
.input-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input-glass:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 
    0 0 0 3px rgba(14, 165, 233, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.1);
}
```

## 🎭 Animation Specifications

### Micro-animations
```css
/* Fade In */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Scale In */
@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Slide In */
@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Pulse */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
```

### Transition Timing
```css
/* Standard transitions */
.transition-fast { transition: all 0.15s ease; }
.transition-normal { transition: all 0.2s ease; }
.transition-slow { transition: all 0.3s ease; }

/* Easing functions */
.ease-bounce { transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55); }
.ease-smooth { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
```

## 📱 Responsive Breakpoints

```css
/* Mobile First Approach */
.container {
  width: 100%;
  padding: 0 16px;
}

@media (min-width: 640px) {  /* sm */
  .container { max-width: 640px; padding: 0 24px; }
}

@media (min-width: 768px) {  /* md */
  .container { max-width: 768px; padding: 0 32px; }
}

@media (min-width: 1024px) { /* lg */
  .container { max-width: 1024px; padding: 0 40px; }
}

@media (min-width: 1280px) { /* xl */
  .container { max-width: 1280px; padding: 0 48px; }
}
```

## 🌓 Dark Mode Support

### Theme Toggle
```css
/* CSS Variables for theme switching */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --glass-bg: rgba(15, 23, 42, 0.3);
  --glass-border: rgba(148, 163, 184, 0.1);
}
```

## 🎯 Performance Guidelines

### Optimization Rules
1. **Backdrop Filter Usage**: Only apply to static elements, avoid on scrolling containers
2. **Layer Management**: Limit stacking contexts to prevent performance issues
3. **Animation Budget**: Keep animations under 200ms for micro-interactions
4. **Blur Intensity**: Use 8-12px blur for most surfaces, 16px maximum
5. **Transparency Levels**: Keep opacity between 0.05-0.3 for readability

### Browser Support
- Chrome 76+ (full support)
- Firefox 103+ (full support)
- Safari 14+ (full support)
- Edge 79+ (full support)

### Fallbacks
```css
/* Fallback for unsupported browsers */
@supports not (backdrop-filter: blur(1px)) {
  .glass-surface {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: none;
  }
}
```
