#!/usr/bin/env python3
"""
Pepe Auth - Lightweight Licensing & Authentication System
A secure, self-hosted licensing platform that outclasses KeyAuth
Built with Python 3.12 + Flask 3 + SQLite + Vanilla JS
"""

import os
import sqlite3
import secrets
import hashlib
import hmac
import json
import time
from datetime import datetime, timedelta
from functools import wraps
from collections import defaultdict

from flask import Flask, request, jsonify, render_template, redirect, url_for, session, flash, send_file
from werkzeug.security import generate_password_hash, check_password_hash
from itsdangerous import URLSafeTimedSerializer, BadSignature, SignatureExpired
import pyotp
from passlib.hash import argon2

# Import our custom modules
from models import Database, User, License, Plan, Analytics, Referral, Giveaway, Event

# Global rate limiting storage (in production, use Redis)
rate_limit_storage = defaultdict(list)

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config.update(
        SECRET_KEY=os.environ.get('SECRET_KEY', secrets.token_hex(32)),
        DATABASE_PATH=os.environ.get('DATABASE_PATH', 'pepe_auth.db'),
        HMAC_SECRET=os.environ.get('HMAC_SECRET', secrets.token_hex(32)),
        JWT_SECRET=os.environ.get('JWT_SECRET', secrets.token_hex(32)),
        SESSION_TIMEOUT=int(os.environ.get('SESSION_TIMEOUT', 3600)),  # 1 hour
        RATE_LIMIT_PER_MINUTE=int(os.environ.get('RATE_LIMIT_PER_MINUTE', 30)),
        ADMIN_REAUTH_TIMEOUT=int(os.environ.get('ADMIN_REAUTH_TIMEOUT', 300)),  # 5 minutes
    )
    
    # Initialize database
    db = Database(app.config['DATABASE_PATH'])
    
    # JWT serializer for session tokens
    jwt_serializer = URLSafeTimedSerializer(app.config['JWT_SECRET'])
    
    # Rate limiting decorator
    def rate_limit(max_requests=None):
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if max_requests is None:
                    limit = app.config['RATE_LIMIT_PER_MINUTE']
                else:
                    limit = max_requests
                
                client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
                now = time.time()
                minute_ago = now - 60
                
                # Clean old entries
                rate_limit_storage[client_ip] = [
                    timestamp for timestamp in rate_limit_storage[client_ip] 
                    if timestamp > minute_ago
                ]
                
                # Check rate limit
                if len(rate_limit_storage[client_ip]) >= limit:
                    return jsonify({'error': 'Rate limit exceeded'}), 429
                
                # Add current request
                rate_limit_storage[client_ip].append(now)
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    # Authentication decorator
    def login_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                return redirect(url_for('auth.login'))
            return f(*args, **kwargs)
        return decorated_function
    
    # Admin role decorator
    def admin_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return jsonify({'error': 'Authentication required'}), 401
            
            user = db.get_user_by_id(session['user_id'])
            if not user or user['role'] not in ['owner', 'admin']:
                return jsonify({'error': 'Admin access required'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    
    # Admin re-authentication decorator for destructive actions
    def admin_reauth_required(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'admin_reauth_time' not in session:
                return jsonify({'error': 'Admin re-authentication required'}), 403
            
            if time.time() - session['admin_reauth_time'] > app.config['ADMIN_REAUTH_TIMEOUT']:
                return jsonify({'error': 'Admin re-authentication expired'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    
    # HMAC signature helpers
    def generate_hmac_signature(data, secret=None):
        """Generate HMAC-SHA256 signature for data"""
        if secret is None:
            secret = app.config['HMAC_SECRET']
        
        if isinstance(data, dict):
            data = json.dumps(data, sort_keys=True)
        
        return hmac.new(
            secret.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def verify_hmac_signature(data, signature, secret=None):
        """Verify HMAC-SHA256 signature"""
        expected_signature = generate_hmac_signature(data, secret)
        return hmac.compare_digest(expected_signature, signature)
    
    # JWT token helpers
    def generate_jwt_token(payload, expires_in=3600):
        """Generate JWT token with expiration"""
        payload['exp'] = time.time() + expires_in
        return jwt_serializer.dumps(payload)
    
    def verify_jwt_token(token, max_age=3600):
        """Verify and decode JWT token"""
        try:
            payload = jwt_serializer.loads(token, max_age=max_age)
            return payload
        except (BadSignature, SignatureExpired):
            return None
    
    # Store helper functions in app context
    app.db = db
    app.generate_hmac_signature = generate_hmac_signature
    app.verify_hmac_signature = verify_hmac_signature
    app.generate_jwt_token = generate_jwt_token
    app.verify_jwt_token = verify_jwt_token
    app.rate_limit = rate_limit
    app.login_required = login_required
    app.admin_required = admin_required
    app.admin_reauth_required = admin_reauth_required

    # Initialize model instances
    app.user_model = User(db)
    app.license_model = License(db)
    app.plan_model = Plan(db)
    app.analytics_model = Analytics(db)
    app.referral_model = Referral(db)
    app.giveaway_model = Giveaway(db)
    app.event_model = Event(db)

    # Register routes
    register_routes(app)

    return app

def register_routes(app):
    """Register all application routes"""

    # Home page
    @app.route('/')
    def index():
        if 'user_id' in session:
            return redirect(url_for('dashboard'))
        return render_template('index.html')

    # Authentication routes
    @app.route('/login', methods=['GET', 'POST'])
    @app.rate_limit(10)  # 10 login attempts per minute
    def login():
        if request.method == 'POST':
            email = request.form.get('email')
            password = request.form.get('password')
            totp_code = request.form.get('totp_code')

            if not email or not password:
                flash('Email and password are required', 'error')
                return render_template('login.html')

            user = app.user_model.get_user_by_email(email)
            if not user or not argon2.verify(password, user['password_hash']):
                app.analytics_model.log_event('login_failed', ip_address=request.remote_addr)
                flash('Invalid email or password', 'error')
                return render_template('login.html')

            # Check TOTP if enabled
            if user['totp_enabled']:
                if not totp_code:
                    flash('TOTP code is required', 'error')
                    return render_template('login.html', totp_required=True, email=email)

                totp = pyotp.TOTP(user['totp_secret'])
                if not totp.verify(totp_code):
                    app.analytics_model.log_event('totp_failed', user_id=user['id'], ip_address=request.remote_addr)
                    flash('Invalid TOTP code', 'error')
                    return render_template('login.html', totp_required=True, email=email)

            # Successful login
            session['user_id'] = user['id']
            session['user_role'] = user['role']
            session.permanent = True

            app.user_model.update_last_login(user['id'], request.remote_addr)
            app.analytics_model.log_event('login_success', user_id=user['id'], ip_address=request.remote_addr)

            flash('Login successful', 'success')
            return redirect(url_for('dashboard'))

        return render_template('login.html')

    @app.route('/register', methods=['GET', 'POST'])
    @app.rate_limit(5)  # 5 registration attempts per minute
    def register():
        if request.method == 'POST':
            email = request.form.get('email')
            username = request.form.get('username')
            password = request.form.get('password')
            referral_code = request.form.get('referral_code')

            if not all([email, username, password]):
                flash('All fields are required', 'error')
                return render_template('register.html')

            # Check if user exists
            if app.user_model.get_user_by_email(email):
                flash('Email already registered', 'error')
                return render_template('register.html')

            # Hash password
            password_hash = argon2.hash(password)

            # Create user
            try:
                user_id = app.user_model.create_user(email, username, password_hash)

                # Generate referral code for new user
                ref_code = secrets.token_urlsafe(8)
                app.referral_model.create_referral_code(user_id, ref_code)

                app.analytics_model.log_event('user_registered', user_id=user_id, ip_address=request.remote_addr)
                flash('Registration successful! Please log in.', 'success')
                return redirect(url_for('login'))

            except Exception as e:
                flash('Registration failed. Please try again.', 'error')
                return render_template('register.html')

        return render_template('register.html')

    @app.route('/logout')
    def logout():
        if 'user_id' in session:
            app.analytics_model.log_event('logout', user_id=session['user_id'], ip_address=request.remote_addr)
        session.clear()
        flash('Logged out successfully', 'success')
        return redirect(url_for('index'))

    # Dashboard and main app routes
    @app.route('/dashboard')
    @app.login_required
    def dashboard():
        user = app.user_model.get_user_by_id(session['user_id'])
        licenses = app.license_model.get_user_licenses(session['user_id'])
        stats = app.analytics_model.get_validation_stats()

        return render_template('dashboard.html', user=user, licenses=licenses, stats=stats)

    @app.route('/licenses')
    @app.login_required
    def licenses():
        user_licenses = app.license_model.get_user_licenses(session['user_id'])
        plans = app.plan_model.get_all_plans()
        return render_template('licenses.html', licenses=user_licenses, plans=plans)

    @app.route('/create-license', methods=['POST'])
    @app.login_required
    def create_license():
        plan_id = request.form.get('plan_id', type=int)
        duration_days = request.form.get('duration_days', type=int, default=30)
        max_devices = request.form.get('max_devices', type=int, default=1)

        if not plan_id:
            flash('Plan selection is required', 'error')
            return redirect(url_for('licenses'))

        # Generate license key
        license_key = f"{secrets.token_hex(4)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(2)}-{secrets.token_hex(6)}"
        expires_at = (datetime.utcnow() + timedelta(days=duration_days)).isoformat()

        try:
            license_id = app.license_model.create_license(
                session['user_id'], plan_id, license_key, expires_at, max_devices
            )
            app.analytics_model.log_event('license_created', user_id=session['user_id'], license_id=license_id)
            flash('License created successfully!', 'success')
        except Exception as e:
            flash('Failed to create license', 'error')

        return redirect(url_for('licenses'))

    # Admin routes
    @app.route('/admin')
    @app.admin_required
    def admin_dashboard():
        # Get admin statistics
        total_users = len(app.db.execute_query("SELECT id FROM users WHERE active = 1"))
        total_licenses = len(app.db.execute_query("SELECT id FROM licenses WHERE active = 1"))
        total_validations = len(app.db.execute_query("SELECT id FROM analytics WHERE action LIKE 'validation_%'"))

        recent_activity = app.db.execute_query("""
            SELECT action, timestamp, ip_address
            FROM analytics
            ORDER BY timestamp DESC
            LIMIT 10
        """)

        return render_template('admin/dashboard.html',
                             total_users=total_users,
                             total_licenses=total_licenses,
                             total_validations=total_validations,
                             recent_activity=recent_activity)

    @app.route('/admin/users')
    @app.admin_required
    def admin_users():
        users = app.db.execute_query("SELECT * FROM users WHERE active = 1 ORDER BY created_at DESC")
        return render_template('admin/users.html', users=users)

    @app.route('/admin/licenses')
    @app.admin_required
    def admin_licenses():
        licenses = app.db.execute_query("""
            SELECT l.*, u.email, p.name as plan_name
            FROM licenses l
            JOIN users u ON l.user_id = u.id
            JOIN plans p ON l.plan_id = p.id
            WHERE l.active = 1
            ORDER BY l.created_at DESC
        """)
        return render_template('admin/licenses.html', licenses=licenses)

    @app.route('/admin/analytics')
    @app.admin_required
    def admin_analytics():
        stats = app.analytics_model.get_validation_stats(30)  # Last 30 days

        # Get daily validation counts for chart
        daily_stats = app.db.execute_query("""
            SELECT DATE(timestamp) as date, COUNT(*) as count
            FROM analytics
            WHERE action LIKE 'validation_%'
            AND timestamp >= date('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        """)

        return render_template('admin/analytics.html', stats=stats, daily_stats=daily_stats)

    # Core License Validation API
    @app.route('/api/validate', methods=['POST'])
    @app.rate_limit(60)  # 60 validation requests per minute
    def validate_license():
        """
        Core license validation endpoint with HMAC verification
        Request: {key, hwid, ts, sig}
        Response: {"status":"ok","session_token":<JWT>,"cdn_token":<signed_url>}
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': 'JSON data required'}), 400

            license_key = data.get('key')
            hwid = data.get('hwid')
            timestamp = data.get('ts')
            signature = data.get('sig')

            if not all([license_key, hwid, timestamp, signature]):
                return jsonify({'error': 'Missing required fields'}), 400

            # Verify timestamp (±120 seconds tolerance)
            current_time = time.time()
            if abs(current_time - timestamp) > 120:
                app.analytics_model.log_event('validation_failed',
                                            ip_address=request.remote_addr,
                                            hwid=hwid,
                                            metadata={'reason': 'timestamp_invalid'})
                return jsonify({'error': 'Invalid timestamp'}), 400

            # Verify HMAC signature
            payload_to_verify = f"{license_key}:{hwid}:{timestamp}"
            if not app.verify_hmac_signature(payload_to_verify, signature):
                app.analytics_model.log_event('validation_failed',
                                            ip_address=request.remote_addr,
                                            hwid=hwid,
                                            metadata={'reason': 'invalid_signature'})
                return jsonify({'error': 'Invalid signature'}), 400

            # Get license from database
            license_data = app.license_model.get_license_by_key(license_key)
            if not license_data:
                app.analytics_model.log_event('validation_failed',
                                            ip_address=request.remote_addr,
                                            hwid=hwid,
                                            metadata={'reason': 'license_not_found'})
                return jsonify({'error': 'Invalid license key'}), 404

            # Check if license is expired
            expires_at = datetime.fromisoformat(license_data['expires_at'])
            if datetime.utcnow() > expires_at:
                app.analytics_model.log_event('validation_failed',
                                            license_id=license_data['id'],
                                            ip_address=request.remote_addr,
                                            hwid=hwid,
                                            metadata={'reason': 'license_expired'})
                return jsonify({'error': 'License expired'}), 403

            # Check blacklist
            blacklist_check = app.db.execute_query("""
                SELECT id FROM blacklist
                WHERE (type = 'ip' AND value = ?)
                   OR (type = 'hwid' AND value = ?)
                   AND active = 1
                   AND (expires_at IS NULL OR expires_at > datetime('now'))
            """, (request.remote_addr, hwid))

            if blacklist_check:
                app.analytics_model.log_event('validation_failed',
                                            license_id=license_data['id'],
                                            ip_address=request.remote_addr,
                                            hwid=hwid,
                                            metadata={'reason': 'blacklisted'})
                return jsonify({'error': 'Access denied'}), 403

            # Check device authorization
            if not app.license_model.is_device_authorized(license_data['id'], hwid):
                # Try to add device if under limit
                if not app.license_model.add_device(license_data['id'], hwid):
                    app.analytics_model.log_event('validation_failed',
                                                license_id=license_data['id'],
                                                ip_address=request.remote_addr,
                                                hwid=hwid,
                                                metadata={'reason': 'device_limit_exceeded'})
                    return jsonify({'error': 'Device limit exceeded'}), 403

            # Update device activity
            app.license_model.update_device_activity(license_data['id'], hwid)

            # Generate session JWT token
            session_payload = {
                'license_id': license_data['id'],
                'user_id': license_data['user_id'],
                'plan_id': license_data['plan_id'],
                'hwid': hwid,
                'max_api_calls': license_data['max_api_calls']
            }
            session_token = app.generate_jwt_token(session_payload, expires_in=3600)

            # Generate CDN token (signed URL for downloads)
            cdn_payload = f"license:{license_data['id']}:user:{license_data['user_id']}:{int(time.time() + 1800)}"  # 30 min
            cdn_token = app.generate_hmac_signature(cdn_payload)

            # Log successful validation
            app.analytics_model.log_event('validation_success',
                                        user_id=license_data['user_id'],
                                        license_id=license_data['id'],
                                        ip_address=request.remote_addr,
                                        hwid=hwid)

            # Update license validation count
            app.db.execute_update("""
                UPDATE licenses
                SET last_validation = ?, validation_count = validation_count + 1
                WHERE id = ?
            """, (datetime.utcnow().isoformat(), license_data['id']))

            return jsonify({
                'status': 'ok',
                'session_token': session_token,
                'cdn_token': cdn_token,
                'expires_at': license_data['expires_at'],
                'plan': license_data['plan_name']
            })

        except Exception as e:
            app.analytics_model.log_event('validation_error',
                                        ip_address=request.remote_addr,
                                        metadata={'error': str(e)})
            return jsonify({'error': 'Internal server error'}), 500

    # Offline license generation endpoint
    @app.route('/api/license/<int:license_id>/offline')
    @app.login_required
    def generate_offline_license(license_id):
        """Generate offline license file for download"""
        # Get license data
        license_data = app.db.execute_query("""
            SELECT l.*, p.name as plan_name, p.max_api_calls, p.concurrent_sessions
            FROM licenses l
            JOIN plans p ON l.plan_id = p.id
            WHERE l.id = ? AND l.user_id = ? AND l.active = 1
        """, (license_id, session['user_id']))

        if not license_data:
            return jsonify({'error': 'License not found'}), 404

        license_info = license_data[0]

        # Create offline license payload
        offline_data = {
            'license_key': license_info['license_key'],
            'user_id': license_info['user_id'],
            'plan_id': license_info['plan_id'],
            'plan_name': license_info['plan_name'],
            'expires_at': license_info['expires_at'],
            'max_devices': license_info['max_devices'],
            'max_api_calls': license_info['max_api_calls'],
            'concurrent_sessions': license_info['concurrent_sessions'],
            'custom_payload': license_info['custom_payload'],
            'issued_at': datetime.utcnow().isoformat(),
            'offline_valid_until': (datetime.utcnow() + timedelta(days=30)).isoformat()
        }

        # Sign the offline license
        signature = app.generate_hmac_signature(json.dumps(offline_data, sort_keys=True))

        offline_license = {
            'data': offline_data,
            'signature': signature,
            'version': '1.0'
        }

        # Log offline license generation
        app.analytics_model.log_event('offline_license_generated',
                                    user_id=session['user_id'],
                                    license_id=license_id)

        return jsonify(offline_license)

if __name__ == '__main__':
    app = create_app()

    # Create default admin user if none exists
    try:
        admin_exists = app.db.execute_query("SELECT id FROM users WHERE role = 'owner' LIMIT 1")
        if not admin_exists:
            print("Creating default admin user...")
            admin_password = argon2.hash('admin123')
            admin_id = app.user_model.create_user(
                '<EMAIL>',
                'admin',
                admin_password,
                'owner'
            )
            print(f"Admin user created with ID: {admin_id}")
            print("Email: <EMAIL>")
            print("Password: admin123")
            print("Please change the password after first login!")
    except Exception as e:
        print(f"Note: {e}")

    print("🐸 Starting Pepe Auth server...")
    print("📍 Server available at: http://localhost:5000")
    print("🔑 Admin login: <EMAIL> / admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)
