/* 
<PERSON><PERSON><PERSON>th - Glassmorphism CSS Framework
Lightweight CSS for beautiful glass effects without build tools
*/

/* Additional glassmorphism utilities */
.glass-subtle {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.glass-strong {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.glass-frosted {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px) saturate(180%);
    -webkit-backdrop-filter: blur(15px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
}

/* Animated glass effects */
.glass-hover {
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-hover:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Glass buttons */
.glass-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 200ms ease;
    cursor: pointer;
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.glass-btn:active {
    transform: translateY(0);
}

/* Glass form elements */
.glass-input {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: white;
    width: 100%;
    transition: all 200ms ease;
}

.glass-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.glass-input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Glass navigation */
.glass-nav {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

/* Glass cards with different variants */
.glass-card-primary {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 16px;
    padding: 2rem;
}

.glass-card-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 16px;
    padding: 2rem;
}

.glass-card-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 16px;
    padding: 2rem;
}

.glass-card-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 16px;
    padding: 2rem;
}

/* Glass table */
.glass-table {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.glass-table th {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
}

.glass-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Glass modal */
.glass-modal {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.glass-modal-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
}

/* Glass loading spinner */
.glass-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid rgba(59, 130, 246, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Glass progress bar */
.glass-progress {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    height: 8px;
    overflow: hidden;
}

.glass-progress-fill {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.8));
    height: 100%;
    border-radius: 20px;
    transition: width 300ms ease;
}

/* Glass tooltip */
.glass-tooltip {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: white;
    font-size: 0.875rem;
    position: absolute;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 200ms ease;
}

.glass-tooltip.show {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .glass-card {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .glass-modal-content {
        padding: 1.5rem;
        border-radius: 16px;
        margin: 1rem;
    }
    
    .glass-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .glass-hover,
    .glass-btn,
    .glass-input,
    .glass-progress-fill {
        transition: none;
    }
    
    .glass-spinner {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass,
    .glass-card,
    .glass-btn,
    .glass-input {
        border-width: 2px;
        background: rgba(255, 255, 255, 0.2);
    }
    
    .glass-btn:hover,
    .glass-input:focus {
        background: rgba(255, 255, 255, 0.3);
    }
}
