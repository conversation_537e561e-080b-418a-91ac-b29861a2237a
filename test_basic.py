#!/usr/bin/env python3
"""
Basic tests for Pepe Auth
Simple test suite to verify core functionality
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from models import Database
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

class TestPepeAuth(unittest.TestCase):
    """Basic test cases for Pepe Auth"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary database for testing
        self.db_fd, self.db_path = tempfile.mkstemp()
        
        # Create test app
        self.app = create_app()
        self.app.config['DATABASE_PATH'] = self.db_path
        self.app.config['TESTING'] = True
        self.app.config['SECRET_KEY'] = 'test-secret-key'
        self.app.config['HMAC_SECRET'] = 'test-hmac-secret'
        self.app.config['JWT_SECRET'] = 'test-jwt-secret'
        
        self.client = self.app.test_client()
        
        # Initialize test database
        with self.app.app_context():
            self.app.db.init_database()
    
    def tearDown(self):
        """Clean up test fixtures"""
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def test_home_page(self):
        """Test that the home page loads"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Pepe Auth', response.data)
    
    def test_login_page(self):
        """Test that the login page loads"""
        response = self.client.get('/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Welcome Back', response.data)
    
    def test_register_page(self):
        """Test that the register page loads"""
        response = self.client.get('/register')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Join Pepe Auth', response.data)
    
    def test_user_registration(self):
        """Test user registration"""
        response = self.client.post('/register', data={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'testpassword123'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        # Should redirect to login page after successful registration
        self.assertIn(b'Login', response.data)
    
    def test_user_login(self):
        """Test user login"""
        # First register a user
        self.client.post('/register', data={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'testpassword123'
        })
        
        # Then try to login
        response = self.client.post('/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        # Should redirect to dashboard after successful login
        self.assertIn(b'Dashboard', response.data)
    
    def test_dashboard_requires_login(self):
        """Test that dashboard requires authentication"""
        response = self.client.get('/dashboard')
        self.assertEqual(response.status_code, 302)  # Should redirect to login
    
    def test_admin_requires_admin_role(self):
        """Test that admin pages require admin role"""
        # Register and login as regular user
        self.client.post('/register', data={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'testpassword123'
        })
        
        self.client.post('/login', data={
            'email': '<EMAIL>',
            'password': 'testpassword123'
        })
        
        # Try to access admin page
        response = self.client.get('/admin')
        self.assertEqual(response.status_code, 403)  # Should be forbidden
    
    def test_license_validation_endpoint(self):
        """Test the license validation API endpoint"""
        # This is a basic test - in reality you'd need valid HMAC signatures
        response = self.client.post('/api/validate', 
                                  json={
                                      'key': 'test-license-key',
                                      'hwid': 'test-hardware-id',
                                      'ts': **********,
                                      'sig': 'test-signature'
                                  })
        
        # Should return an error for invalid signature
        self.assertEqual(response.status_code, 400)
        data = response.get_json()
        self.assertIn('error', data)
    
    def test_rate_limiting(self):
        """Test that rate limiting works"""
        # Make multiple requests quickly
        responses = []
        for i in range(35):  # More than the 30 req/min limit
            response = self.client.get('/login')
            responses.append(response.status_code)
        
        # Should get rate limited
        self.assertIn(429, responses)
    
    def test_database_initialization(self):
        """Test that database initializes correctly"""
        with self.app.app_context():
            # Check that tables exist
            tables = self.app.db.execute_query("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            
            table_names = [table['name'] for table in tables]
            
            # Check for essential tables
            self.assertIn('users', table_names)
            self.assertIn('licenses', table_names)
            self.assertIn('plans', table_names)
            self.assertIn('analytics', table_names)
    
    def test_hmac_signature_generation(self):
        """Test HMAC signature generation"""
        with self.app.app_context():
            test_data = "test data"
            signature1 = self.app.generate_hmac_signature(test_data)
            signature2 = self.app.generate_hmac_signature(test_data)
            
            # Same data should produce same signature
            self.assertEqual(signature1, signature2)
            
            # Should be able to verify signature
            self.assertTrue(self.app.verify_hmac_signature(test_data, signature1))
            
            # Wrong signature should fail
            self.assertFalse(self.app.verify_hmac_signature(test_data, "wrong-signature"))
    
    def test_jwt_token_generation(self):
        """Test JWT token generation and verification"""
        with self.app.app_context():
            payload = {'user_id': 123, 'test': 'data'}
            token = self.app.generate_jwt_token(payload)
            
            # Should be able to verify token
            decoded = self.app.verify_jwt_token(token)
            self.assertIsNotNone(decoded)
            self.assertEqual(decoded['user_id'], 123)
            self.assertEqual(decoded['test'], 'data')
            
            # Invalid token should return None
            invalid_decoded = self.app.verify_jwt_token("invalid-token")
            self.assertIsNone(invalid_decoded)

def run_tests():
    """Run the test suite"""
    print("🧪 Running Pepe Auth test suite...")
    print("=" * 50)
    
    # Check if required modules can be imported
    try:
        import flask
        import passlib
        import itsdangerous
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Run: pip install -r requirements.txt")
        return False
    
    # Run tests
    unittest.main(verbosity=2, exit=False)
    return True

if __name__ == '__main__':
    success = run_tests()
    if success:
        print("\n✅ All tests completed!")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
