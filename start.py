#!/usr/bin/env python3
"""
Pepe Auth - Quick Start Script
Run this to start the application instantly!
"""
import os
import sys
import subprocess

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "src/backend/requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        sys.exit(1)

def setup_database():
    """Initialize the database"""
    print("🗄️  Setting up database...")
    
    # Change to backend directory
    os.chdir("src/backend")
    
    # Set environment
    os.environ['FLASK_APP'] = 'run.py'
    os.environ['FLASK_ENV'] = 'development'
    
    try:
        # Initialize database
        subprocess.check_call([sys.executable, "-m", "flask", "init-db"])
        print("✅ Database initialized!")
        
        # Create admin user
        print("👤 Creating admin user...")
        print("   Email: <EMAIL>")
        print("   Username: admin")
        print("   Password: admin123")
        
        subprocess.check_call([
            sys.executable, "-m", "flask", "create-admin",
            "--email", "<EMAIL>",
            "--username", "admin", 
            "--password", "admin123"
        ])
        print("✅ Admin user created!")

        # Seed demo data
        print("🌱 Creating demo data...")
        subprocess.check_call([sys.executable, "-m", "flask", "seed-data"])
        print("✅ Demo data created!")

    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        sys.exit(1)

def start_server():
    """Start the Flask development server"""
    print("🚀 Starting Pepe Auth server...")
    print("📍 Server will be available at: http://localhost:5000")
    print("🔑 Admin login: <EMAIL> / admin123")
    print("\n" + "="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")
    
    try:
        subprocess.check_call([sys.executable, "run.py"])
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Thanks for using Pepe Auth!")
    except subprocess.CalledProcessError:
        print("❌ Failed to start server")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🎉 Welcome to Pepe Auth!")
    print("Next-Generation Licensing & Authentication Platform")
    print("="*50)
    
    # Check if we're in the right directory
    if not os.path.exists("src/backend/requirements.txt"):
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Install requirements
    install_requirements()
    
    # Setup database
    setup_database()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
