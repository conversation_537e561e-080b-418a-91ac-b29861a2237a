#!/usr/bin/env python3
"""
Pepe Auth - Quick Start Script
Simple script to run the Pepe Auth licensing platform
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def main():
    """Main function to start Pepe Auth"""
    print("🐸 Pepe Auth - Lightweight Licensing Platform")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found")
        sys.exit(1)
    
    # Install dependencies if needed
    try:
        import flask
        import passlib
        import itsdangerous
    except ImportError:
        print("📦 Some dependencies are missing. Installing...")
        if not install_dependencies():
            sys.exit(1)
    
    # Import and run the app
    try:
        from app import create_app
        app = create_app()
        
        print("\n🚀 Starting Pepe Auth server...")
        print("📍 Server will be available at: http://localhost:5000")
        print("🔑 Default admin login: <EMAIL> / admin123")
        print("\n" + "=" * 50)
        print("Press Ctrl+C to stop the server")
        print("=" * 50 + "\n")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Thanks for using Pepe Auth!")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
