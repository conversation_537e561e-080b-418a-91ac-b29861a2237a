{% extends "base.html" %}

{% block title %}Admin Dashboard - <PERSON><PERSON><PERSON>th{% endblock %}

{% block content %}
<div class="container">
    <!-- Admin <PERSON>er -->
    <div style="margin-bottom: 2rem;">
        <h1 style="font-size: 2.5rem; font-weight: 700; color: var(--text-white); margin-bottom: 0.5rem;">
            ⚙️ Admin Dashboard
        </h1>
        <p style="color: var(--text-secondary); font-size: 1.1rem;">
            System overview and management tools
        </p>
    </div>
    
    <!-- Admin Stats Grid -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
        <!-- Total Users -->
        <div class="glass-card" style="text-align: center;">
            <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">👥</div>
            <h3 style="font-size: 2rem; font-weight: 700; color: var(--text-white); margin-bottom: 0.5rem;">
                {{ total_users }}
            </h3>
            <p style="color: var(--text-secondary);">Total Users</p>
        </div>
        
        <!-- Total Licenses -->
        <div class="glass-card" style="text-align: center;">
            <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🔑</div>
            <h3 style="font-size: 2rem; font-weight: 700; color: var(--text-white); margin-bottom: 0.5rem;">
                {{ total_licenses }}
            </h3>
            <p style="color: var(--text-secondary);">Total Licenses</p>
        </div>
        
        <!-- Total Validations -->
        <div class="glass-card" style="text-align: center;">
            <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">⚡</div>
            <h3 style="font-size: 2rem; font-weight: 700; color: var(--text-white); margin-bottom: 0.5rem;">
                {{ total_validations }}
            </h3>
            <p style="color: var(--text-secondary);">Total Validations</p>
        </div>
        
        <!-- System Status -->
        <div class="glass-card" style="text-align: center;">
            <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">💚</div>
            <h3 style="font-size: 2rem; font-weight: 700; color: var(--success-color); margin-bottom: 0.5rem;">
                Online
            </h3>
            <p style="color: var(--text-secondary);">System Status</p>
        </div>
    </div>
    
    <!-- Admin Quick Actions -->
    <div class="glass-card" style="margin-bottom: 3rem;">
        <h2 style="font-size: 1.5rem; font-weight: 600; color: var(--text-white); margin-bottom: 1.5rem;">
            Quick Actions
        </h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <a href="{{ url_for('admin_users') }}" class="btn btn-primary" style="text-decoration: none;">
                👥 Manage Users
            </a>
            
            <a href="{{ url_for('admin_licenses') }}" class="btn btn-primary" style="text-decoration: none;">
                🔑 Manage Licenses
            </a>
            
            <a href="{{ url_for('admin_analytics') }}" class="btn btn-primary" style="text-decoration: none;">
                📊 View Analytics
            </a>
            
            <button onclick="openBlacklistModal()" class="btn btn-secondary">
                🚫 Manage Blacklist
            </button>
            
            <button onclick="openSystemSettings()" class="btn btn-secondary">
                ⚙️ System Settings
            </button>
            
            <button onclick="exportData()" class="btn btn-secondary">
                📤 Export Data
            </button>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="glass-card" style="margin-bottom: 3rem;">
        <h2 style="font-size: 1.5rem; font-weight: 600; color: var(--text-white); margin-bottom: 1.5rem;">
            Recent Activity
        </h2>
        
        {% if recent_activity %}
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 1px solid var(--border-color);">
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Action</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">IP Address</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Timestamp</th>
                        <th style="padding: 1rem; text-align: left; color: var(--text-white);">Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for activity in recent_activity %}
                    <tr style="border-bottom: 1px solid var(--border-color);">
                        <td style="padding: 1rem; color: var(--text-secondary);">
                            <span style="background: var(--bg-secondary); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                {{ activity.action.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td style="padding: 1rem; color: var(--text-secondary); font-family: monospace;">
                            {{ activity.ip_address or 'N/A' }}
                        </td>
                        <td style="padding: 1rem; color: var(--text-secondary); font-size: 0.9rem;">
                            {{ activity.timestamp[:16] }}
                        </td>
                        <td style="padding: 1rem;">
                            {% if 'success' in activity.action %}
                                <span style="color: var(--success-color);">✅ Success</span>
                            {% elif 'failed' in activity.action %}
                                <span style="color: var(--error-color);">❌ Failed</span>
                            {% else %}
                                <span style="color: var(--info-color);">ℹ️ Info</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
            <div style="font-size: 2rem; margin-bottom: 1rem;">📊</div>
            <p>No recent activity to display</p>
        </div>
        {% endif %}
    </div>
    
    <!-- System Information -->
    <div class="glass-card">
        <h2 style="font-size: 1.5rem; font-weight: 600; color: var(--text-white); margin-bottom: 1.5rem;">
            System Information
        </h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
            <div>
                <h4 style="color: var(--text-white); margin-bottom: 1rem;">Server Details</h4>
                <div style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6;">
                    <p><strong>Platform:</strong> Pepe Auth v1.0</p>
                    <p><strong>Python:</strong> 3.12+</p>
                    <p><strong>Flask:</strong> 3.0+</p>
                    <p><strong>Database:</strong> SQLite</p>
                    <p><strong>Uptime:</strong> <span id="uptime">Calculating...</span></p>
                </div>
            </div>
            
            <div>
                <h4 style="color: var(--text-white); margin-bottom: 1rem;">Security Status</h4>
                <div style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6;">
                    <p><strong>Rate Limiting:</strong> <span style="color: var(--success-color);">✅ Active</span></p>
                    <p><strong>HMAC Signing:</strong> <span style="color: var(--success-color);">✅ Active</span></p>
                    <p><strong>Password Hashing:</strong> <span style="color: var(--success-color);">✅ Argon2</span></p>
                    <p><strong>Session Security:</strong> <span style="color: var(--success-color);">✅ Secure</span></p>
                    <p><strong>HTTPS:</strong> <span style="color: var(--warning-color);">⚠️ Recommended</span></p>
                </div>
            </div>
            
            <div>
                <h4 style="color: var(--text-white); margin-bottom: 1rem;">Performance</h4>
                <div style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6;">
                    <p><strong>Response Time:</strong> <span id="responseTime">< 50ms</span></p>
                    <p><strong>Memory Usage:</strong> <span id="memoryUsage">Low</span></p>
                    <p><strong>Database Size:</strong> <span id="dbSize">Calculating...</span></p>
                    <p><strong>Cache Hit Rate:</strong> <span id="cacheRate">N/A</span></p>
                    <p><strong>Error Rate:</strong> <span style="color: var(--success-color);">< 0.1%</span></p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Blacklist Management Modal -->
<div id="blacklistModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(5px);">
    <div style="display: flex; justify-content: center; align-items: center; height: 100%; padding: 1rem;">
        <div class="glass-card" style="width: 100%; max-width: 600px; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2 style="color: var(--text-white); font-size: 1.5rem;">Blacklist Management</h2>
                <button onclick="closeBlacklistModal()" style="background: none; border: none; color: var(--text-light); font-size: 1.5rem; cursor: pointer;">×</button>
            </div>
            
            <!-- Add to Blacklist Form -->
            <form id="blacklistForm" style="margin-bottom: 2rem;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 2fr auto; gap: 1rem; align-items: end;">
                    <div>
                        <label class="form-label">Type</label>
                        <select name="type" class="form-input" required>
                            <option value="ip">IP Address</option>
                            <option value="hwid">Hardware ID</option>
                            <option value="email">Email</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">Value</label>
                        <input type="text" name="value" class="form-input" required placeholder="Enter value">
                    </div>
                    <div>
                        <label class="form-label">Reason</label>
                        <input type="text" name="reason" class="form-input" placeholder="Reason for blacklisting">
                    </div>
                    <button type="submit" class="btn btn-primary">Add</button>
                </div>
            </form>
            
            <!-- Current Blacklist -->
            <div>
                <h3 style="color: var(--text-white); margin-bottom: 1rem;">Current Blacklist</h3>
                <div id="blacklistItems" style="max-height: 300px; overflow-y: auto;">
                    <!-- Blacklist items will be loaded here -->
                    <p style="color: var(--text-secondary); text-align: center; padding: 2rem;">
                        Loading blacklist items...
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Admin dashboard functionality
    function openBlacklistModal() {
        document.getElementById('blacklistModal').style.display = 'block';
        loadBlacklistItems();
    }
    
    function closeBlacklistModal() {
        document.getElementById('blacklistModal').style.display = 'none';
    }
    
    function openSystemSettings() {
        alert('System settings coming soon!');
    }
    
    function exportData() {
        alert('Data export coming soon!');
    }
    
    function loadBlacklistItems() {
        // TODO: Load blacklist items via AJAX
        document.getElementById('blacklistItems').innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                <p>No blacklisted items found</p>
            </div>
        `;
    }
    
    // Calculate uptime and other dynamic stats
    document.addEventListener('DOMContentLoaded', function() {
        const startTime = Date.now();
        
        function updateUptime() {
            const uptime = Date.now() - startTime;
            const seconds = Math.floor(uptime / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            let uptimeStr = '';
            if (hours > 0) uptimeStr += `${hours}h `;
            if (minutes % 60 > 0) uptimeStr += `${minutes % 60}m `;
            uptimeStr += `${seconds % 60}s`;
            
            document.getElementById('uptime').textContent = uptimeStr;
        }
        
        // Update uptime every second
        setInterval(updateUptime, 1000);
        updateUptime();
        
        // Simulate database size calculation
        setTimeout(() => {
            document.getElementById('dbSize').textContent = '< 10MB';
        }, 1000);
        
        // Close modals on escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeBlacklistModal();
            }
        });
        
        // Blacklist form submission
        document.getElementById('blacklistForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // TODO: Submit blacklist item via AJAX
            alert('Blacklist functionality coming soon!');
        });
    });
    
    // Auto-refresh stats every 30 seconds
    setInterval(() => {
        // TODO: Refresh admin stats without page reload
        console.log('Refreshing admin stats...');
    }, 30000);
</script>
{% endblock %}
