#!/usr/bin/env python3
"""
Production Deployment Script for Pepe Auth
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print deployment banner"""
    print("""
🐸 Pepe Auth - Production Deployment
=====================================
Preparing your license management system for production...
""")

def check_requirements():
    """Check system requirements"""
    print("📋 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Check if we're in the right directory
    if not os.path.exists("src/backend/app"):
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    print("✅ Project structure verified")

def setup_environment():
    """Setup production environment"""
    print("\n🔧 Setting up production environment...")
    
    # Create production directories
    os.makedirs("logs", exist_ok=True)
    os.makedirs("backups", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    print("✅ Created production directories")
    
    # Create production environment file
    env_content = """# Production Environment Configuration
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-super-secret-production-key-change-this
JWT_SECRET_KEY=your-jwt-secret-production-key-change-this

# Database (change to PostgreSQL/MySQL for production)
DATABASE_URL=sqlite:///pepe_auth_production.db

# Security
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# Email Configuration (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Performance
SQLALCHEMY_POOL_SIZE=10
SQLALCHEMY_POOL_TIMEOUT=20
SQLALCHEMY_POOL_RECYCLE=3600
"""
    
    with open(".env.production", "w") as f:
        f.write(env_content)
    print("✅ Created production environment file (.env.production)")

def install_dependencies():
    """Install production dependencies"""
    print("\n📦 Installing production dependencies...")
    
    os.chdir("src/backend")
    
    try:
        # Install production requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        
        # Install production server
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "gunicorn", "gevent"
        ])
        
        print("✅ Dependencies installed successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)
    
    os.chdir("../..")

def setup_database():
    """Setup production database"""
    print("\n🗄️ Setting up production database...")
    
    os.chdir("src/backend")
    
    try:
        # Initialize database
        subprocess.check_call([sys.executable, "-m", "flask", "init-db"])
        print("✅ Database initialized")
        
        # Create admin user
        print("\n👤 Creating admin user...")
        print("Please provide admin credentials:")
        subprocess.check_call([
            sys.executable, "-m", "flask", "create-admin",
            "--email", "<EMAIL>",
            "--username", "admin"
        ])
        print("✅ Admin user created")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        sys.exit(1)
    
    os.chdir("../..")

def create_systemd_service():
    """Create systemd service file"""
    print("\n🔧 Creating systemd service...")
    
    current_dir = os.getcwd()
    service_content = f"""[Unit]
Description=Pepe Auth License Management System
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory={current_dir}/src/backend
Environment=PATH={current_dir}/venv/bin
ExecStart={current_dir}/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 4 --worker-class gevent --worker-connections 1000 --timeout 30 --keep-alive 2 --max-requests 1000 --max-requests-jitter 50 app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open("pepe-auth.service", "w") as f:
        f.write(service_content)
    
    print("✅ Systemd service file created (pepe-auth.service)")
    print("   To install: sudo cp pepe-auth.service /etc/systemd/system/")
    print("   To enable: sudo systemctl enable pepe-auth")
    print("   To start: sudo systemctl start pepe-auth")

def create_nginx_config():
    """Create nginx configuration"""
    print("\n🌐 Creating nginx configuration...")
    
    nginx_content = """server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /auth/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files
    location /static/ {
        alias /path/to/pepe-auth/src/backend/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
"""
    
    with open("nginx-pepe-auth.conf", "w") as f:
        f.write(nginx_content)
    
    print("✅ Nginx configuration created (nginx-pepe-auth.conf)")
    print("   To install: sudo cp nginx-pepe-auth.conf /etc/nginx/sites-available/")
    print("   To enable: sudo ln -s /etc/nginx/sites-available/nginx-pepe-auth.conf /etc/nginx/sites-enabled/")

def create_docker_files():
    """Create Docker configuration"""
    print("\n🐳 Creating Docker configuration...")
    
    dockerfile_content = """FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY src/backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir gunicorn gevent

# Copy application code
COPY src/backend/ .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:5000/health || exit 1

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--worker-class", "gevent", "app:app"]
"""
    
    with open("Dockerfile", "w") as f:
        f.write(dockerfile_content)
    
    docker_compose_content = """version: '3.8'

services:
  pepe-auth:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***************************************/pepe_auth
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./backups:/app/backups
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=pepe_auth
      - POSTGRES_USER=pepe_auth
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - pepe-auth
    restart: unless-stopped

volumes:
  postgres_data:
"""
    
    with open("docker-compose.yml", "w") as f:
        f.write(docker_compose_content)
    
    print("✅ Docker configuration created")
    print("   To build: docker-compose build")
    print("   To run: docker-compose up -d")

def print_deployment_summary():
    """Print deployment summary"""
    print("""
🎉 Production Deployment Complete!

📁 Files Created:
   - .env.production (environment configuration)
   - pepe-auth.service (systemd service)
   - nginx-pepe-auth.conf (nginx configuration)
   - Dockerfile (Docker container)
   - docker-compose.yml (Docker Compose)

🚀 Next Steps:

1. Manual Deployment:
   - Copy pepe-auth.service to /etc/systemd/system/
   - Configure nginx with the provided config
   - Start the service: sudo systemctl start pepe-auth

2. Docker Deployment:
   - Run: docker-compose up -d

3. Security:
   - Change SECRET_KEY and JWT_SECRET_KEY in .env.production
   - Configure SSL certificates
   - Set up firewall rules
   - Configure database backups

4. Monitoring:
   - Set up log rotation
   - Configure monitoring alerts
   - Test backup and restore procedures

📊 Access Points:
   - Web Interface: https://your-domain.com
   - Admin Dashboard: https://your-domain.com/admin/dashboard
   - API Documentation: https://your-domain.com/docs
   - System Status: https://your-domain.com/status

🔒 Security Checklist:
   ✅ Change default secret keys
   ✅ Configure SSL/TLS certificates
   ✅ Set up firewall rules
   ✅ Configure database backups
   ✅ Enable log monitoring
   ✅ Test disaster recovery

Happy licensing! 🐸
""")

def main():
    """Main deployment function"""
    print_banner()
    check_requirements()
    setup_environment()
    install_dependencies()
    setup_database()
    create_systemd_service()
    create_nginx_config()
    create_docker_files()
    print_deployment_summary()

if __name__ == "__main__":
    main()
