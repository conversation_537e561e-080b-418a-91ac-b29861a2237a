#!/usr/bin/env python3
"""
Comprehensive System Test Suite for Pepe Auth
"""
import os
import sys
import time
import requests
import json
import subprocess
from datetime import datetime

class PepeAuthTester:
    """Test suite for Pepe Auth system"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message="", duration=0):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'duration': duration
        })
        print(f"{status} {test_name} ({duration:.2f}s)")
        if message:
            print(f"    {message}")
    
    def test_server_health(self):
        """Test server health and basic connectivity"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                self.log_test("Server Health Check", True, "Server is responding", duration)
                return True
            else:
                self.log_test("Server Health Check", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Server Health Check", False, str(e), duration)
            return False
    
    def test_homepage_load(self):
        """Test homepage loading"""
        start_time = time.time()
        try:
            response = self.session.get(self.base_url, timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200 and "Pepe Auth" in response.text:
                self.log_test("Homepage Load", True, "Homepage loaded successfully", duration)
                return True
            else:
                self.log_test("Homepage Load", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Homepage Load", False, str(e), duration)
            return False
    
    def test_user_registration(self):
        """Test user registration"""
        start_time = time.time()
        try:
            # Get registration page first to get CSRF token
            reg_page = self.session.get(f"{self.base_url}/auth/register")
            
            # Extract CSRF token (simplified)
            csrf_token = "test-token"  # In real implementation, parse from HTML
            
            # Test registration
            test_user = {
                'username': f'testuser_{int(time.time())}',
                'email': f'test_{int(time.time())}@example.com',
                'password': 'TestPassword123!',
                'confirm_password': 'TestPassword123!',
                'csrf_token': csrf_token
            }
            
            response = self.session.post(
                f"{self.base_url}/auth/register",
                data=test_user,
                timeout=10
            )
            duration = time.time() - start_time
            
            if response.status_code in [200, 302]:  # Success or redirect
                self.log_test("User Registration", True, "Registration successful", duration)
                return True
            else:
                self.log_test("User Registration", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("User Registration", False, str(e), duration)
            return False
    
    def test_admin_login(self):
        """Test admin login"""
        start_time = time.time()
        try:
            # Get login page
            login_page = self.session.get(f"{self.base_url}/auth/login")
            
            # Login with admin credentials
            login_data = {
                'email': '<EMAIL>',
                'password': 'admin123',
                'csrf_token': 'test-token'
            }
            
            response = self.session.post(
                f"{self.base_url}/auth/login",
                data=login_data,
                timeout=10
            )
            duration = time.time() - start_time
            
            if response.status_code in [200, 302]:
                self.log_test("Admin Login", True, "Login successful", duration)
                return True
            else:
                self.log_test("Admin Login", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Admin Login", False, str(e), duration)
            return False
    
    def test_dashboard_access(self):
        """Test dashboard access"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/dashboard", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200 and "Dashboard" in response.text:
                self.log_test("Dashboard Access", True, "Dashboard accessible", duration)
                return True
            else:
                self.log_test("Dashboard Access", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Dashboard Access", False, str(e), duration)
            return False
    
    def test_license_creation(self):
        """Test license creation"""
        start_time = time.time()
        try:
            # Access license creation page
            response = self.session.get(f"{self.base_url}/licenses/create", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                self.log_test("License Creation Page", True, "License creation page accessible", duration)
                return True
            else:
                self.log_test("License Creation Page", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("License Creation Page", False, str(e), duration)
            return False
    
    def test_api_validation_endpoint(self):
        """Test license validation API endpoint"""
        start_time = time.time()
        try:
            # Test validation endpoint with dummy data
            validation_data = {
                'license_key': 'TEST-XXXX-XXXX-XXXX-XXXXXXXXXXXX',
                'hwid': 'test-hardware-id',
                'timestamp': int(time.time()),
                'signature': 'test-signature'
            }
            
            response = self.session.post(
                f"{self.base_url}/licenses/api/validate",
                json=validation_data,
                timeout=10
            )
            duration = time.time() - start_time
            
            # Should return 404 for non-existent license, which is expected
            if response.status_code in [404, 400]:
                self.log_test("API Validation Endpoint", True, "Endpoint responding correctly", duration)
                return True
            else:
                self.log_test("API Validation Endpoint", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("API Validation Endpoint", False, str(e), duration)
            return False
    
    def test_status_page(self):
        """Test system status page"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/status", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200 and "System Status" in response.text:
                self.log_test("Status Page", True, "Status page accessible", duration)
                return True
            else:
                self.log_test("Status Page", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Status Page", False, str(e), duration)
            return False
    
    def test_documentation_page(self):
        """Test documentation page"""
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            duration = time.time() - start_time
            
            if response.status_code == 200 and "Documentation" in response.text:
                self.log_test("Documentation Page", True, "Documentation accessible", duration)
                return True
            else:
                self.log_test("Documentation Page", False, f"Status: {response.status_code}", duration)
                return False
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Documentation Page", False, str(e), duration)
            return False
    
    def test_performance(self):
        """Test system performance"""
        print("\n🚀 Performance Testing...")
        
        # Test multiple concurrent requests
        start_time = time.time()
        success_count = 0
        total_requests = 10
        
        for i in range(total_requests):
            try:
                response = self.session.get(f"{self.base_url}/health", timeout=5)
                if response.status_code == 200:
                    success_count += 1
            except:
                pass
        
        duration = time.time() - start_time
        success_rate = (success_count / total_requests) * 100
        avg_response_time = duration / total_requests
        
        if success_rate >= 90 and avg_response_time < 1.0:
            self.log_test("Performance Test", True, 
                         f"Success rate: {success_rate:.1f}%, Avg response: {avg_response_time:.3f}s", 
                         duration)
        else:
            self.log_test("Performance Test", False, 
                         f"Success rate: {success_rate:.1f}%, Avg response: {avg_response_time:.3f}s", 
                         duration)
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting Pepe Auth System Tests...\n")
        
        # Core functionality tests
        print("🔧 Core Functionality Tests:")
        self.test_server_health()
        self.test_homepage_load()
        self.test_status_page()
        self.test_documentation_page()
        
        print("\n👤 Authentication Tests:")
        self.test_user_registration()
        self.test_admin_login()
        self.test_dashboard_access()
        
        print("\n🔑 License Management Tests:")
        self.test_license_creation()
        self.test_api_validation_endpoint()
        
        # Performance tests
        self.test_performance()
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*60)
        print("📊 TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Save detailed report
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': (passed_tests/total_tests)*100
            },
            'tests': self.test_results
        }
        
        with open('test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print("\n📄 Detailed report saved to: test_report.json")


def main():
    """Main test function"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"🎯 Testing Pepe Auth at: {base_url}")
    
    tester = PepeAuthTester(base_url)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
